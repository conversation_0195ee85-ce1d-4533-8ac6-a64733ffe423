# 三优CRM功能需求与现有项目对比分析

## 📊 总体评估

| 模块 | 符合度 | 状态 | 说明 |
|------|--------|------|------|
| 客户管理 | 85% | ✅ 基本符合 | 核心功能完整，需完善审核流程 |
| 销售管理 | 80% | ✅ 基本符合 | 线索和拜访管理完善 |
| 市场管理 | 10% | ❌ 几乎缺失 | 活动管理功能完全缺失 |
| 销售人员管理 | 60% | ⚠️ 部分符合 | 缺少区域分配和离职管理 |
| 数据看板 | 75% | ✅ 基本符合 | 缺少市场活动统计 |
| 基础设置 | 85% | ✅ 基本符合 | 缺少管线配置 |
| 系统管理 | 95% | ✅ 完全符合 | 若依框架提供完整功能 |

**整体符合度：65%**

---

## 1. 客户管理模块对比

### 1.1 客户档案

| 功能需求 | 现有实现 | 符合度 | 备注 |
|----------|----------|--------|------|
| 管理客户档案信息 | ✅ CustomerInformation + CusCustomerOrClue | 90% | 存在两套实现，需统一 |
| 客户基本信息+人员信息 | ✅ customer_information + customer_contact | 95% | 数据结构完整 |
| 分配主管业务员 | ✅ current_user_id字段 | 90% | 基本功能完整 |
| 业务员添加，管理员审核 | ❌ 缺少审核流程 | 30% | **需新增审核状态和流程** |
| 审核前业务员编辑，审核后管理员编辑 | ❌ 缺少权限控制 | 20% | **需新增权限控制逻辑** |
| 根据区域自动分配业务员 | ❌ 缺少自动分配逻辑 | 10% | **需新增自动分配算法** |
| 多个负责人时管理员分配 | ⚠️ 部分支持 | 60% | 需完善分配策略 |
| 支持导出Excel | ✅ 已实现 | 95% | 功能完整 |

**冗余功能：**
- `CustomerInformation` 和 `CusCustomerOrClue` 重复实现
- `CustomerContact` 和 `CusCcContact` 重复实现

### 1.2 客户人员

| 功能需求 | 现有实现 | 符合度 | 备注 |
|----------|----------|--------|------|
| 管理客户人员信息 | ✅ CustomerContact表 | 95% | 数据结构完整 |
| 一个客户包含多个人员 | ✅ ci_id外键关联 | 95% | 关联关系正确 |

---

## 2. 销售管理模块对比

### 2.1 线索档案

| 功能需求 | 现有实现 | 符合度 | 备注 |
|----------|----------|--------|------|
| 线索管理（按所属权查看） | ✅ CusCustomerOrClue | 90% | 权限控制完善 |
| 线索基本操作（增删改查） | ✅ 完整CRUD | 95% | 功能完整 |
| 线索来源字段 | ✅ CusSource管理 | 95% | 来源管理完善 |
| 业务员线索跟进 | ✅ CusCcFollow | 90% | 跟进功能完整 |
| 管理员查看所有线索 | ✅ 权限控制 | 90% | 权限设计合理 |
| 支持导出线索信息 | ✅ 已实现 | 95% | 导出功能完整 |
| 支持导入线索信息 | ✅ 已实现 | 90% | 导入功能基本完整 |

### 2.2 线索分配

| 功能需求 | 现有实现 | 符合度 | 备注 |
|----------|----------|--------|------|
| 线索添加者为负责人 | ✅ current_user_id | 95% | 逻辑正确 |
| 管理员重新分配 | ✅ 分配功能 | 90% | 功能完整 |

### 2.3 拜访管理

| 功能需求 | 现有实现 | 符合度 | 备注 |
|----------|----------|--------|------|
| 客户线索拜访记录 | ✅ CustomerVisit | 90% | 功能完整 |
| 记录时间、内容、方式、客户等 | ✅ 字段完整 | 95% | 数据结构合理 |

**冗余功能：**
- `CustomerFollow` 和 `CusCcFollow` 重复实现跟进记录

---

## 3. 市场管理模块对比

### 3.1 活动分类

| 功能需求 | 现有实现 | 符合度 | 备注 |
|----------|----------|--------|------|
| 线上活动/线下活动分类 | ❌ 完全缺失 | 0% | **需新增活动分类管理** |

### 3.2 活动管理

| 功能需求 | 现有实现 | 符合度 | 备注 |
|----------|----------|--------|------|
| 管理活动信息 | ❌ 完全缺失 | 0% | **需新增活动管理模块** |
| 多维度查询条件 | ❌ 完全缺失 | 0% | **需新增查询功能** |
| 活动基本操作（增删改查） | ❌ 完全缺失 | 0% | **需新增CRUD功能** |
| 活动时间、地点、参与人员等 | ❌ 完全缺失 | 0% | **需新增数据表和字段** |
| 支持导出活动信息 | ❌ 完全缺失 | 0% | **需新增导出功能** |

**缺失数据表：**
- `market_activity_category` - 活动分类表
- `market_activity` - 市场活动表  
- `market_activity_participant` - 活动参与人员表

---

## 4. 销售人员管理模块对比

### 4.1 人员管理

| 功能需求 | 现有实现 | 符合度 | 备注 |
|----------|----------|--------|------|
| 管理销售人员档案 | ✅ sys_user | 90% | 若依用户管理 |
| 信息来源于ERP系统 | ❌ 缺少ERP集成 | 20% | **需新增ERP集成** |
| 支持编辑功能 | ✅ 用户编辑 | 95% | 功能完整 |
| 支持导出 | ✅ 已实现 | 90% | 导出功能完整 |

### 4.2 区域分配

| 功能需求 | 现有实现 | 符合度 | 备注 |
|----------|----------|--------|------|
| 管理销售人员管辖区域 | ❌ 缺少区域分配 | 10% | **需新增区域分配管理** |
| 多维度查询 | ❌ 缺少查询功能 | 0% | **需新增查询界面** |
| 多人负责同一区域 | ❌ 缺少多人分配 | 0% | **需新增多人分配逻辑** |

**缺失数据表：**
- `sales_region` - 区域管理表
- `sales_user_region` - 销售人员区域分配表

### 4.3 客户分配

| 功能需求 | 现有实现 | 符合度 | 备注 |
|----------|----------|--------|------|
| 销售人员管理自己客户 | ✅ 权限控制 | 90% | 权限设计合理 |
| 多维度查询客户 | ✅ 查询功能 | 85% | 查询功能较完整 |
| 多人负责同一客户 | ⚠️ 部分支持 | 60% | 需完善多人分配 |

### 4.4 离职管理

| 功能需求 | 现有实现 | 符合度 | 备注 |
|----------|----------|--------|------|
| 客户批量交接 | ❌ 完全缺失 | 0% | **需新增批量交接功能** |
| 选择性客户交接 | ❌ 完全缺失 | 0% | **需新增选择交接界面** |
| 线索交接 | ❌ 完全缺失 | 0% | **需新增线索交接功能** |
| 区域交接 | ❌ 完全缺失 | 0% | **需新增区域交接功能** |

**缺失数据表：**
- `resignation_handover` - 离职交接记录表

---

## 5. 数据看板模块对比

### 5.1 客户统计

| 功能需求 | 现有实现 | 符合度 | 备注 |
|----------|----------|--------|------|
| 统计活跃度/签单率 | ✅ KanbanController | 80% | 基本统计功能 |
| 按季度统计 | ⚠️ 部分支持 | 70% | 需完善季度统计 |

### 5.2 市场活动统计

| 功能需求 | 现有实现 | 符合度 | 备注 |
|----------|----------|--------|------|
| 统计客户覆盖率 | ❌ 缺少活动统计 | 0% | **依赖市场活动模块** |
| 按区域统计覆盖率 | ❌ 缺少区域统计 | 0% | **需新增区域统计** |

### 5.3 订单报表

| 功能需求 | 现有实现 | 符合度 | 备注 |
|----------|----------|--------|------|
| 统计成单金额 | ✅ 业绩统计 | 85% | 基本功能完整 |
| 按季度、区域、销售人员统计 | ⚠️ 部分支持 | 70% | 需完善多维度统计 |

---

## 6. 基础设置模块对比

### 6.1 区域管理

| 功能需求 | 现有实现 | 符合度 | 备注 |
|----------|----------|--------|------|
| 国家、省、市管理 | ✅ sys_base_config | 80% | 基础配置支持 |
| 增删改功能 | ✅ 配置管理 | 85% | 功能基本完整 |

### 6.2 管线配置

| 功能需求 | 现有实现 | 符合度 | 备注 |
|----------|----------|--------|------|
| 管线字段配置 | ❌ 缺少管线配置 | 10% | **需新增管线配置模块** |

**缺失数据表：**
- `sales_pipeline` - 管线配置表

### 6.3 需求分类

| 功能需求 | 现有实现 | 符合度 | 备注 |
|----------|----------|--------|------|
| 线索需求分类配置 | ✅ BusinessType | 90% | 业务类型配置 |

### 6.4 线索来源

| 功能需求 | 现有实现 | 符合度 | 备注 |
|----------|----------|--------|------|
| 线索来源配置 | ✅ CusSource | 95% | 来源配置完整 |

---

## 7. 系统管理模块对比

### 7.1 用户管理

| 功能需求 | 现有实现 | 符合度 | 备注 |
|----------|----------|--------|------|
| ERP系统授权体系 | ❌ 缺少ERP集成 | 30% | **需新增ERP集成** |
| 用户信息记录 | ✅ sys_user | 95% | 若依用户管理 |
| 密码修改 | ✅ 密码管理 | 95% | 功能完整 |

### 7.2 数据字段

| 功能需求 | 现有实现 | 符合度 | 备注 |
|----------|----------|--------|------|
| 统一化配置信息 | ✅ sys_dict_* | 90% | 数据字典完整 |

### 7.3 角色/权限

| 功能需求 | 现有实现 | 符合度 | 备注 |
|----------|----------|--------|------|
| 角色权限管理 | ✅ RBAC权限 | 95% | 若依权限管理完整 |

---

## 🗑️ 冗余功能清单

### 完全冗余的模块（建议移除）
- **财务管理模块** (`frontend/src/views/finance/`)
  - FinanceJournalController
  - TurnoverStatementController
  - 所有财务相关表和实体

- **证书管理模块** (`frontend/src/views/certificate/`)
  - 证书相关Controller和Service
  - 证书相关数据表

- **材料管理模块** (`frontend/src/views/customer/material-*/`)
  - 材料入库、出库、交接功能
  - 材料相关数据表

- **风险管理模块** (`frontend/src/views/customer/risk-*/`)
  - 风险审核、风险清理功能
  - 风险相关数据表

### 重复实现的功能（需要统一）
- **客户管理重复**
  - `CustomerInformation` vs `CusCustomerOrClue`
  - 建议：统一使用 `CusCustomerOrClue`

- **联系人管理重复**
  - `CustomerContact` vs `CusCcContact`
  - 建议：统一使用 `CusCcContact`

- **跟进记录重复**
  - `CustomerFollow` vs `CusCcFollow`
  - 建议：统一使用 `CusCcFollow`

### 过度复杂的功能（需要简化）
- **工作流模块** - BizFlow相关功能过于复杂
- **标签系统** - CusTag功能过度设计
- **公海配置** - CusSea配置项过多

---

## ❌ 缺失功能清单

### 高优先级缺失功能
1. **市场活动管理模块**
   - 活动分类管理
   - 活动信息管理（增删改查）
   - 活动参与人员管理
   - 活动统计分析

2. **销售人员区域分配**
   - 区域管理界面
   - 销售人员区域分配
   - 多人负责同一区域

3. **离职管理功能**
   - 客户批量交接
   - 线索批量交接
   - 区域交接
   - 交接记录管理

### 中优先级缺失功能
1. **客户审核流程**
   - 审核状态管理
   - 审核权限控制
   - 审核流程界面

2. **自动分配逻辑**
   - 基于区域的自动分配
   - 分配策略配置
   - 分配历史记录

3. **管线配置**
   - 管线阶段配置
   - 管线数据源管理

### 低优先级缺失功能
1. **ERP系统集成**
   - 用户授权集成
   - 人员信息同步

2. **高级统计功能**
   - 季度统计优化
   - 多维度分析
   - 自定义报表

---

## 💾 数据库表结构分析

### 现有表结构匹配度：70%

#### ✅ 完全匹配的表
- `sys_user` - 用户管理
- `sys_role` - 角色管理
- `sys_menu` - 菜单权限
- `cus_customer_or_clue` - 线索/客户管理
- `cus_source` - 线索来源
- `customer_contact` - 客户联系人

#### ⚠️ 部分匹配的表
- `customer_information` - 与CusCustomerOrClue重复
- `customer_follow` - 与CusCcFollow重复
- `sys_base_config` - 需要扩展区域配置

#### ❌ 完全缺失的表
```sql
-- 市场活动相关
market_activity_category     -- 活动分类
market_activity             -- 市场活动
market_activity_participant -- 活动参与人员

-- 销售区域相关
sales_region                -- 区域管理
sales_user_region          -- 销售人员区域分配

-- 离职管理相关
resignation_handover       -- 离职交接记录

-- 基础配置相关
sales_pipeline             -- 管线配置
```

---

## 🎯 优化实施建议

### 第一阶段：代码重构和冗余清理（2-3周）
1. **统一数据模型**
   - 合并客户管理：CustomerInformation → CusCustomerOrClue
   - 合并联系人管理：CustomerContact → CusCcContact
   - 合并跟进记录：CustomerFollow → CusCcFollow

2. **移除冗余模块**
   - 删除财务管理模块
   - 删除证书管理模块
   - 删除材料管理模块
   - 删除风险管理模块

### 第二阶段：核心缺失功能开发（4-6周）
1. **市场活动管理模块**
   - 数据库表设计和创建
   - 后端API开发
   - 前端页面开发

2. **销售人员管理完善**
   - 区域分配管理
   - 离职交接管理

### 第三阶段：业务流程优化（2-3周）
1. **客户审核流程**
2. **自动分配逻辑**
3. **管线配置功能**

### 第四阶段：系统集成和优化（2-4周）
1. **ERP系统集成**
2. **性能优化**
3. **用户体验优化**

---

## 📈 预期收益

### 代码质量提升
- 消除重复代码，提高可维护性
- 统一数据模型，减少数据不一致
- 简化系统架构，降低复杂度

### 功能完整性提升
- 补齐市场活动管理短板
- 完善销售人员管理功能
- 优化业务流程体验

### 系统性能提升
- 减少冗余模块，提高系统性能
- 优化数据库结构，提高查询效率
- 简化前端页面，提高用户体验

**预计整体符合度可从65%提升至95%以上**

---

## 📋 详细实施计划

### 数据迁移脚本示例

#### 1. 客户数据统一迁移
```sql
-- 将CustomerInformation数据迁移到CusCustomerOrClue
INSERT INTO cus_customer_or_clue (
    company_name, type, current_user_id, create_time, update_time
)
SELECT
    customer_name, '1' as type, manger, create_time, update_time
FROM customer_information
WHERE is_deleted = 0;
```

#### 2. 联系人数据统一迁移
```sql
-- 将CustomerContact数据迁移到CusCcContact
INSERT INTO cus_cc_contact (
    cc_id, contact_name, contact_phone, post, create_time
)
SELECT
    ci_id, name, phone, dept, create_time
FROM customer_contact
WHERE is_deleted = 0;
```

### 新增表结构SQL

#### 市场活动相关表
```sql
-- 活动分类表
CREATE TABLE `market_activity_category` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `category_name` varchar(50) NOT NULL COMMENT '分类名称',
  `category_type` varchar(20) NOT NULL COMMENT '活动类型：online-线上，offline-线下',
  `sort_order` int(4) DEFAULT 0 COMMENT '排序',
  `status` char(1) DEFAULT '1' COMMENT '状态：0-停用，1-启用',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `is_deleted` tinyint(1) DEFAULT 0 COMMENT '逻辑删除标志',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='市场活动分类表';

-- 市场活动表
CREATE TABLE `market_activity` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '活动ID',
  `activity_name` varchar(100) NOT NULL COMMENT '活动名称',
  `category_id` bigint(20) NOT NULL COMMENT '活动分类ID',
  `activity_time` datetime COMMENT '活动时间',
  `activity_location` varchar(200) COMMENT '活动地点',
  `activity_type` varchar(20) COMMENT '活动方式',
  `organizer` varchar(100) COMMENT '组织者',
  `participant_count` int(11) DEFAULT 0 COMMENT '参与人数',
  `description` text COMMENT '活动描述',
  `status` char(1) DEFAULT '1' COMMENT '状态：0-已取消，1-进行中，2-已结束',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `is_deleted` tinyint(1) DEFAULT 0 COMMENT '逻辑删除标志',
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_activity_time` (`activity_time`)
) ENGINE=InnoDB COMMENT='市场活动表';

-- 活动参与人员表
CREATE TABLE `market_activity_participant` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '参与记录ID',
  `activity_id` bigint(20) NOT NULL COMMENT '活动ID',
  `customer_id` bigint(20) COMMENT '客户ID',
  `contact_id` bigint(20) COMMENT '联系人ID',
  `participant_name` varchar(50) COMMENT '参与者姓名',
  `participant_phone` varchar(20) COMMENT '参与者电话',
  `participant_company` varchar(100) COMMENT '参与者公司',
  `registration_time` datetime COMMENT '报名时间',
  `attendance_status` char(1) DEFAULT '0' COMMENT '出席状态：0-未出席，1-已出席',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `is_deleted` tinyint(1) DEFAULT 0 COMMENT '逻辑删除标志',
  PRIMARY KEY (`id`),
  KEY `idx_activity_id` (`activity_id`),
  KEY `idx_customer_id` (`customer_id`)
) ENGINE=InnoDB COMMENT='活动参与人员表';
```

#### 销售区域管理表
```sql
-- 区域管理表
CREATE TABLE `sales_region` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '区域ID',
  `region_name` varchar(100) NOT NULL COMMENT '区域名称',
  `region_code` varchar(50) COMMENT '区域编码',
  `country` varchar(50) COMMENT '国家',
  `province` varchar(50) COMMENT '省份',
  `city` varchar(50) COMMENT '城市',
  `district` varchar(50) COMMENT '区县',
  `parent_id` bigint(20) DEFAULT 0 COMMENT '父区域ID',
  `level` int(4) DEFAULT 1 COMMENT '区域层级',
  `sort_order` int(4) DEFAULT 0 COMMENT '排序',
  `status` char(1) DEFAULT '1' COMMENT '状态：0-停用，1-启用',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `is_deleted` tinyint(1) DEFAULT 0 COMMENT '逻辑删除标志',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_region_code` (`region_code`)
) ENGINE=InnoDB COMMENT='销售区域管理表';

-- 销售人员区域分配表
CREATE TABLE `sales_user_region` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分配ID',
  `user_id` bigint(20) NOT NULL COMMENT '销售人员ID',
  `region_id` bigint(20) NOT NULL COMMENT '区域ID',
  `is_primary` tinyint(1) DEFAULT 0 COMMENT '是否主要负责：0-否，1-是',
  `assign_time` datetime COMMENT '分配时间',
  `assign_by` varchar(64) COMMENT '分配人',
  `status` char(1) DEFAULT '1' COMMENT '状态：0-停用，1-启用',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `is_deleted` tinyint(1) DEFAULT 0 COMMENT '逻辑删除标志',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_region` (`user_id`, `region_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_region_id` (`region_id`)
) ENGINE=InnoDB COMMENT='销售人员区域分配表';
```

#### 离职交接管理表
```sql
-- 离职交接记录表
CREATE TABLE `resignation_handover` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '交接记录ID',
  `from_user_id` bigint(20) NOT NULL COMMENT '离职员工ID',
  `to_user_id` bigint(20) NOT NULL COMMENT '接收员工ID',
  `handover_type` varchar(20) NOT NULL COMMENT '交接类型：customer-客户，clue-线索，region-区域',
  `resource_id` bigint(20) NOT NULL COMMENT '资源ID',
  `resource_name` varchar(200) COMMENT '资源名称',
  `handover_time` datetime COMMENT '交接时间',
  `handover_reason` varchar(500) COMMENT '交接原因',
  `handover_status` char(1) DEFAULT '0' COMMENT '交接状态：0-待确认，1-已确认，2-已拒绝',
  `confirm_time` datetime COMMENT '确认时间',
  `remark` varchar(500) COMMENT '备注',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `is_deleted` tinyint(1) DEFAULT 0 COMMENT '逻辑删除标志',
  PRIMARY KEY (`id`),
  KEY `idx_from_user` (`from_user_id`),
  KEY `idx_to_user` (`to_user_id`),
  KEY `idx_handover_type` (`handover_type`),
  KEY `idx_resource_id` (`resource_id`)
) ENGINE=InnoDB COMMENT='离职交接记录表';
```

#### 管线配置表
```sql
-- 销售管线配置表
CREATE TABLE `sales_pipeline` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '管线ID',
  `pipeline_name` varchar(50) NOT NULL COMMENT '管线名称',
  `pipeline_code` varchar(30) NOT NULL COMMENT '管线编码',
  `pipeline_color` varchar(10) COMMENT '管线颜色',
  `pipeline_order` int(4) DEFAULT 0 COMMENT '排序',
  `win_probability` decimal(5,2) DEFAULT 0.00 COMMENT '赢单概率(%)',
  `description` varchar(200) COMMENT '描述',
  `status` char(1) DEFAULT '1' COMMENT '状态：0-停用，1-启用',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `is_deleted` tinyint(1) DEFAULT 0 COMMENT '逻辑删除标志',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_pipeline_code` (`pipeline_code`),
  KEY `idx_pipeline_order` (`pipeline_order`)
) ENGINE=InnoDB COMMENT='销售管线配置表';
```

### 现有表结构优化
```sql
-- 为客户线索表添加审核相关字段
ALTER TABLE `cus_customer_or_clue`
ADD COLUMN `audit_status` varchar(20) DEFAULT 'pending' COMMENT '审核状态：pending-待审核，approved-已审核，rejected-已拒绝',
ADD COLUMN `audit_by` varchar(64) COMMENT '审核人',
ADD COLUMN `audit_time` datetime COMMENT '审核时间',
ADD COLUMN `audit_remark` varchar(500) COMMENT '审核备注',
ADD COLUMN `auto_assigned` tinyint(1) DEFAULT 0 COMMENT '是否自动分配：0-否，1-是',
ADD COLUMN `pipeline_id` bigint(20) COMMENT '管线阶段ID';

-- 添加索引
ALTER TABLE `cus_customer_or_clue`
ADD INDEX `idx_audit_status` (`audit_status`),
ADD INDEX `idx_pipeline_id` (`pipeline_id`);
```

---

## 🚀 快速开始指南

### 1. 环境准备
- 备份现有数据库
- 准备测试环境
- 确认依赖版本

### 2. 第一步：数据模型统一
```bash
# 1. 执行数据迁移脚本
mysql -u username -p database_name < migration_scripts.sql

# 2. 验证数据迁移结果
# 3. 更新后端实体类
# 4. 更新前端API调用
```

### 3. 第二步：移除冗余模块
```bash
# 1. 注释掉冗余模块的路由
# 2. 备份冗余代码
# 3. 逐步删除冗余文件
# 4. 清理数据库表
```

### 4. 第三步：新增核心功能
```bash
# 1. 创建新的数据表
# 2. 生成后端代码框架
# 3. 开发前端页面
# 4. 集成测试
```

通过这个详细的对比分析文档，可以清晰地看到现有项目与三优CRM功能需求的差距，以及具体的优化方向和实施计划。
